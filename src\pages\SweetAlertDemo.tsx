import React from 'react';
import { 
  showSuccessToast, 
  showErrorToast, 
  showWarningToast, 
  showInfoToast,
  showDeleteConfirmation,
  showBlacklistConfirmation,
  showUnblacklistConfirmation,
  showConfirmation,
  showLoading,
  closeAlert,
  showSuccess,
  showError
} from '../utils/sweetAlert';
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  X, 
  Trash2, 
  Ban, 
  UserCheck,
  Loader,
  Bell
} from 'lucide-react';

const SweetAlertDemo: React.FC = () => {
  const handleSuccessToast = () => {
    showSuccessToast('Your disaster report has been successfully submitted and is being processed by our emergency response team.', 'Report Submitted Successfully');
  };

  const handleErrorToast = () => {
    showErrorToast('Unable to connect to the emergency response system. Please check your connection and try again.', 'Connection Failed');
  };

  const handleWarningToast = () => {
    showWarningToast('Your session will expire in 5 minutes. Please save your work to avoid losing any data.', 'Session Expiring Soon');
  };

  const handleInfoToast = () => {
    showInfoToast('New emergency protocols have been updated. Please review the latest guidelines in your dashboard.', 'System Update Available');
  };

  const handleDeleteConfirmation = async () => {
    const result = await showDeleteConfirmation(
      'John Doe',
      'user',
      'This will permanently remove all user data and cannot be undone.'
    );
    
    if (result.isConfirmed) {
      showSuccessToast('User deleted successfully!');
    }
  };

  const handleBlacklistConfirmation = async () => {
    const result = await showBlacklistConfirmation('Jane Smith');
    
    if (result.isConfirmed) {
      showSuccessToast('User has been blacklisted successfully!');
    }
  };

  const handleUnblacklistConfirmation = async () => {
    const result = await showUnblacklistConfirmation('Mike Johnson');
    
    if (result.isConfirmed) {
      showSuccessToast('User access has been restored successfully!');
    }
  };

  const handleGenericConfirmation = async () => {
    const result = await showConfirmation(
      'Confirm Action',
      'Are you sure you want to proceed with this action?',
      'Yes, proceed',
      'Cancel',
      'question'
    );
    
    if (result.isConfirmed) {
      showSuccessToast('Action confirmed!');
    }
  };

  const handleLoadingDemo = () => {
    showLoading('Processing your request...');
    
    setTimeout(() => {
      closeAlert();
      showSuccessToast('Processing completed!');
    }, 3000);
  };

  const handleSuccessDialog = () => {
    showSuccess(
      'Mission Accomplished!',
      'Your disaster report has been successfully submitted and is now being reviewed by our emergency response team.',
      'Great!'
    );
  };

  const handleErrorDialog = () => {
    showError(
      'Connection Failed',
      'Unable to connect to the emergency response system. Please check your internet connection and try again.',
      'Retry'
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            SweetAlert2 Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Explore the modern, styled notifications and confirmations used throughout the DisasterWatch application.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Toast Notifications */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <Bell className="w-5 h-5 mr-2 text-blue-600" />
              Toast Notifications
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleSuccessToast}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Success Toast
              </button>
              <button
                onClick={handleErrorToast}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <X className="w-4 h-4 mr-2" />
                Error Toast
              </button>
              <button
                onClick={handleWarningToast}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                Warning Toast
              </button>
              <button
                onClick={handleInfoToast}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Info className="w-4 h-4 mr-2" />
                Info Toast
              </button>
            </div>
          </div>

          {/* Confirmation Dialogs */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              Confirmations
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleDeleteConfirmation}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete User
              </button>
              <button
                onClick={handleBlacklistConfirmation}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Ban className="w-4 h-4 mr-2" />
                Blacklist User
              </button>
              <button
                onClick={handleUnblacklistConfirmation}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <UserCheck className="w-4 h-4 mr-2" />
                Restore Access
              </button>
              <button
                onClick={handleGenericConfirmation}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Info className="w-4 h-4 mr-2" />
                Generic Confirm
              </button>
            </div>
          </div>

          {/* Special Dialogs */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <Loader className="w-5 h-5 mr-2 text-purple-600" />
              Special Dialogs
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleLoadingDemo}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Loader className="w-4 h-4 mr-2" />
                Loading Dialog
              </button>
              <button
                onClick={handleSuccessDialog}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Success Dialog
              </button>
              <button
                onClick={handleErrorDialog}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <X className="w-4 h-4 mr-2" />
                Error Dialog
              </button>
            </div>
          </div>
        </div>

        <div className="mt-12 bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Toast Notifications</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Auto-dismiss with timer</li>
                <li>• Pause on hover</li>
                <li>• Progress bar indicator</li>
                <li>• Brand-consistent colors</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Confirmation Dialogs</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Detailed action descriptions</li>
                <li>• Consequence warnings</li>
                <li>• Styled buttons with hover effects</li>
                <li>• Keyboard navigation support</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SweetAlertDemo;
