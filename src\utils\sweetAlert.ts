import Swal from 'sweetalert2';

// Enhanced SweetAlert2 configuration for DisasterWatch application
// Beautiful, modern styling with professional appearance

/**
 * Enhanced default configuration for all SweetAlert2 instances
 */
const defaultConfig = {
  background: '#ffffff',
  color: '#1f2937',
  showClass: {
    popup: 'animate__animated animate__fadeInDown animate__faster'
  },
  hideClass: {
    popup: 'animate__animated animate__fadeOutUp animate__faster'
  },
  customClass: {
    popup: 'swal2-modern-popup',
    title: 'swal2-modern-title',
    htmlContainer: 'swal2-modern-content',
    confirmButton: 'swal2-modern-confirm',
    cancelButton: 'swal2-modern-cancel',
    denyButton: 'swal2-modern-deny',
    actions: 'swal2-modern-actions'
  },
  buttonsStyling: false,
  reverseButtons: true,
  focusConfirm: false,
  allowOutsideClick: true,
  allowEscapeKey: true,
  backdrop: `
    rgba(0,0,0,0.4)
    url("/images/nyan-cat.gif")
    left top
    no-repeat
  `,
  width: '32rem',
  padding: '2rem',
  borderRadius: '1rem'
};

/**
 * Beautiful success notification toast
 */
export const showSuccessToast = (message: string, title?: string) => {
  return Swal.fire({
    icon: 'success',
    title: title || 'Success',
    text: message,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 4000,
    timerProgressBar: true,
    background: 'linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)',
    color: '#065f46',
    iconColor: '#10b981',
    customClass: {
      popup: 'swal2-success-toast',
      title: 'swal2-success-title',
      htmlContainer: 'swal2-success-content',
      timerProgressBar: 'swal2-success-progress'
    },
    didOpen: (toast) => {
      // Add beautiful styling
      toast.style.border = '1px solid #a7f3d0';
      toast.style.borderLeft = '4px solid #10b981';
      toast.style.borderRadius = '12px';
      toast.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05)';
      toast.style.fontFamily = 'Inter, system-ui, sans-serif';

      // Pause on hover
      toast.addEventListener('mouseenter', Swal.stopTimer);
      toast.addEventListener('mouseleave', Swal.resumeTimer);
    }
  });
};

/**
 * Beautiful error notification toast
 */
export const showErrorToast = (message: string, title?: string) => {
  return Swal.fire({
    icon: 'error',
    title: title || 'Error',
    text: message,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 5000,
    timerProgressBar: true,
    background: 'linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%)',
    color: '#7f1d1d',
    iconColor: '#ef4444',
    customClass: {
      popup: 'swal2-error-toast',
      title: 'swal2-error-title',
      htmlContainer: 'swal2-error-content',
      timerProgressBar: 'swal2-error-progress'
    },
    didOpen: (toast) => {
      // Add beautiful styling
      toast.style.border = '1px solid #fecaca';
      toast.style.borderLeft = '4px solid #ef4444';
      toast.style.borderRadius = '12px';
      toast.style.boxShadow = '0 10px 25px rgba(239, 68, 68, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05)';
      toast.style.fontFamily = 'Inter, system-ui, sans-serif';

      // Pause on hover
      toast.addEventListener('mouseenter', Swal.stopTimer);
      toast.addEventListener('mouseleave', Swal.resumeTimer);
    }
  });
};

/**
 * Beautiful warning notification toast
 */
export const showWarningToast = (message: string, title?: string) => {
  return Swal.fire({
    icon: 'warning',
    title: title || 'Warning',
    text: message,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 4500,
    timerProgressBar: true,
    background: 'linear-gradient(135deg, #fffbeb 0%, #fefce8 100%)',
    color: '#78350f',
    iconColor: '#f59e0b',
    customClass: {
      popup: 'swal2-warning-toast',
      title: 'swal2-warning-title',
      htmlContainer: 'swal2-warning-content',
      timerProgressBar: 'swal2-warning-progress'
    },
    didOpen: (toast) => {
      // Add beautiful styling
      toast.style.border = '1px solid #fed7aa';
      toast.style.borderLeft = '4px solid #f59e0b';
      toast.style.borderRadius = '12px';
      toast.style.boxShadow = '0 10px 25px rgba(245, 158, 11, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05)';
      toast.style.fontFamily = 'Inter, system-ui, sans-serif';

      // Pause on hover
      toast.addEventListener('mouseenter', Swal.stopTimer);
      toast.addEventListener('mouseleave', Swal.resumeTimer);
    }
  });
};

/**
 * Beautiful info notification toast
 */
export const showInfoToast = (message: string, title?: string) => {
  return Swal.fire({
    icon: 'info',
    title: title || 'Information',
    text: message,
    toast: true,
    position: 'top-end',
    showConfirmButton: false,
    timer: 4000,
    timerProgressBar: true,
    background: 'linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%)',
    color: '#1e3a8a',
    iconColor: '#3b82f6',
    customClass: {
      popup: 'swal2-info-toast',
      title: 'swal2-info-title',
      htmlContainer: 'swal2-info-content',
      timerProgressBar: 'swal2-info-progress'
    },
    didOpen: (toast) => {
      // Add beautiful styling
      toast.style.border = '1px solid #bfdbfe';
      toast.style.borderLeft = '4px solid #3b82f6';
      toast.style.borderRadius = '12px';
      toast.style.boxShadow = '0 10px 25px rgba(59, 130, 246, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05)';
      toast.style.fontFamily = 'Inter, system-ui, sans-serif';

      // Pause on hover
      toast.addEventListener('mouseenter', Swal.stopTimer);
      toast.addEventListener('mouseleave', Swal.resumeTimer);
    }
  });
};

/**
 * Beautiful confirmation dialog for destructive actions
 */
export const showDeleteConfirmation = (
  itemName: string,
  itemType: string = 'item',
  additionalWarning?: string
) => {
  return Swal.fire({
    icon: 'warning',
    title: `Delete ${itemType}?`,
    html: `
      <div style="text-align: left; font-family: Inter, system-ui, sans-serif;">
        <p style="color: #374151; margin-bottom: 16px; font-size: 16px; line-height: 1.5;">
          Are you sure you want to delete <strong style="color: #dc2626; font-weight: 600;">${itemName}</strong>?
        </p>
        ${additionalWarning ? `
          <div style="background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
                      border: 1px solid #fecaca;
                      border-left: 4px solid #ef4444;
                      padding: 16px;
                      border-radius: 12px;
                      margin-bottom: 16px;
                      box-shadow: 0 4px 6px rgba(239, 68, 68, 0.1);">
            <p style="color: #b91c1c; font-size: 14px; font-weight: 500; margin: 0;">${additionalWarning}</p>
          </div>
        ` : ''}
        <p style="color: #6b7280; font-size: 14px; margin: 0; font-style: italic;">This action cannot be undone.</p>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: `Yes, delete ${itemType}`,
    cancelButtonText: 'Cancel',
    background: 'linear-gradient(135deg, #ffffff 0%, #f9fafb 100%)',
    width: '28rem',
    padding: '2rem',
    borderRadius: '16px',
    customClass: {
      popup: 'swal2-delete-popup',
      confirmButton: 'swal2-delete-confirm',
      cancelButton: 'swal2-delete-cancel'
    },
    didOpen: (popup) => {
      // Style the popup
      popup.style.border = '1px solid #e5e7eb';
      popup.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1)';

      // Style confirm button
      const confirmBtn = popup.querySelector('.swal2-delete-confirm') as HTMLElement;
      if (confirmBtn) {
        confirmBtn.style.background = 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
        confirmBtn.style.color = 'white';
        confirmBtn.style.border = 'none';
        confirmBtn.style.padding = '12px 24px';
        confirmBtn.style.borderRadius = '12px';
        confirmBtn.style.fontWeight = '600';
        confirmBtn.style.fontSize = '14px';
        confirmBtn.style.cursor = 'pointer';
        confirmBtn.style.transition = 'all 0.3s ease';
        confirmBtn.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
        confirmBtn.style.marginRight = '12px';

        confirmBtn.addEventListener('mouseenter', () => {
          confirmBtn.style.transform = 'translateY(-2px)';
          confirmBtn.style.boxShadow = '0 8px 20px rgba(220, 38, 38, 0.4)';
        });

        confirmBtn.addEventListener('mouseleave', () => {
          confirmBtn.style.transform = 'translateY(0)';
          confirmBtn.style.boxShadow = '0 4px 12px rgba(220, 38, 38, 0.3)';
        });
      }

      // Style cancel button
      const cancelBtn = popup.querySelector('.swal2-delete-cancel') as HTMLElement;
      if (cancelBtn) {
        cancelBtn.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';
        cancelBtn.style.color = '#374151';
        cancelBtn.style.border = '1px solid #d1d5db';
        cancelBtn.style.padding = '12px 24px';
        cancelBtn.style.borderRadius = '12px';
        cancelBtn.style.fontWeight = '600';
        cancelBtn.style.fontSize = '14px';
        cancelBtn.style.cursor = 'pointer';
        cancelBtn.style.transition = 'all 0.3s ease';

        cancelBtn.addEventListener('mouseenter', () => {
          cancelBtn.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';
          cancelBtn.style.transform = 'translateY(-1px)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
          cancelBtn.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';
          cancelBtn.style.transform = 'translateY(0)';
        });
      }
    }
  });
};

/**
 * Confirmation dialog for user blacklisting
 */
export const showBlacklistConfirmation = (userName: string) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'warning',
    title: 'Blacklist User?',
    html: `
      <div class="text-left">
        <p class="text-gray-700 mb-3">
          Are you sure you want to blacklist <strong class="text-red-600">${userName}</strong>?
        </p>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-3">
          <p class="text-sm text-red-700 font-medium mb-2">This will:</p>
          <ul class="text-sm text-red-600 space-y-1">
            <li>• Suspend their account immediately</li>
            <li>• Prevent them from accessing the system</li>
            <li>• Require admin intervention to restore access</li>
          </ul>
        </div>
        <p class="text-sm text-gray-500">This action can be reversed later if needed.</p>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Yes, blacklist user',
    cancelButtonText: 'Cancel',
    customClass: {
      ...defaultConfig.customClass,
      confirmButton: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mr-3'
    }
  });
};

/**
 * Confirmation dialog for user unblacklisting
 */
export const showUnblacklistConfirmation = (userName: string) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'question',
    title: 'Restore User Access?',
    html: `
      <div class="text-left">
        <p class="text-gray-700 mb-3">
          Are you sure you want to restore access for <strong class="text-green-600">${userName}</strong>?
        </p>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-3">
          <p class="text-sm text-green-700 font-medium mb-2">This will:</p>
          <ul class="text-sm text-green-600 space-y-1">
            <li>• Reactivate their account</li>
            <li>• Allow them to access the system again</li>
            <li>• Restore their previous permissions</li>
          </ul>
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Yes, restore access',
    cancelButtonText: 'Cancel',
    customClass: {
      ...defaultConfig.customClass,
      confirmButton: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mr-3'
    }
  });
};

/**
 * Generic confirmation dialog
 */
export const showConfirmation = (
  title: string,
  message: string,
  confirmText: string = 'Confirm',
  cancelText: string = 'Cancel',
  type: 'warning' | 'question' | 'info' = 'question'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: type,
    title,
    text: message,
    showCancelButton: true,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText
  });
};

/**
 * Beautiful loading dialog for async operations
 */
export const showLoading = (message: string = 'Processing...') => {
  return Swal.fire({
    title: message,
    html: `
      <div style="display: flex; align-items: center; justify-content: center; margin: 20px 0;">
        <div style="
          width: 40px;
          height: 40px;
          border: 3px solid #e5e7eb;
          border-top: 3px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        "></div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `,
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    width: '24rem',
    padding: '2rem',
    borderRadius: '16px',
    color: '#1f2937',
    didOpen: (popup) => {
      popup.style.border = '1px solid #e5e7eb';
      popup.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05)';
      popup.style.fontFamily = 'Inter, system-ui, sans-serif';

      // Style the title
      const title = popup.querySelector('.swal2-title') as HTMLElement;
      if (title) {
        title.style.fontSize = '18px';
        title.style.fontWeight = '600';
        title.style.color = '#374151';
        title.style.marginBottom = '8px';
      }
    }
  });
};

/**
 * Close any open SweetAlert2 dialog
 */
export const closeAlert = () => {
  Swal.close();
};

/**
 * Success dialog for completed operations
 */
export const showSuccess = (
  title: string,
  message: string,
  confirmText: string = 'OK'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'success',
    title,
    text: message,
    confirmButtonText: confirmText,
    customClass: {
      ...defaultConfig.customClass,
      confirmButton: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl'
    }
  });
};

/**
 * Error dialog for failed operations
 */
export const showError = (
  title: string,
  message: string,
  confirmText: string = 'OK'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'error',
    title,
    text: message,
    confirmButtonText: confirmText,
    customClass: {
      ...defaultConfig.customClass,
      confirmButton: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl'
    }
  });
};
