<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SweetAlert2 Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f3f4f6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        button {
            background: linear-gradient(to right, #2563eb, #4f46e5);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 8px;
            cursor: pointer;
            font-weight: 600;
        }
        button:hover {
            background: linear-gradient(to right, #1d4ed8, #4338ca);
        }
        .success { background: linear-gradient(to right, #10b981, #059669); }
        .error { background: linear-gradient(to right, #ef4444, #dc2626); }
        .warning { background: linear-gradient(to right, #f59e0b, #d97706); }
    </style>
</head>
<body>
    <div class="container">
        <h1>SweetAlert2 Implementation Test</h1>
        <p>Test the SweetAlert2 notifications that have been implemented in the DisasterWatch application.</p>
        
        <h3>Toast Notifications</h3>
        <button onclick="showSuccessToast()">Success Toast</button>
        <button onclick="showErrorToast()" class="error">Error Toast</button>
        <button onclick="showWarningToast()" class="warning">Warning Toast</button>
        <button onclick="showInfoToast()">Info Toast</button>
        
        <h3>Confirmation Dialogs</h3>
        <button onclick="showDeleteConfirmation()" class="error">Delete Confirmation</button>
        <button onclick="showBlacklistConfirmation()" class="warning">Blacklist Confirmation</button>
        <button onclick="showGenericConfirmation()">Generic Confirmation</button>
        
        <h3>Special Dialogs</h3>
        <button onclick="showLoadingDemo()">Loading Demo</button>
        <button onclick="showSuccessDialog()" class="success">Success Dialog</button>
        <button onclick="showErrorDialog()" class="error">Error Dialog</button>
    </div>

    <script>
        // Default configuration matching our implementation
        const defaultConfig = {
            customClass: {
                popup: 'rounded-2xl shadow-2xl border-0',
                title: 'text-2xl font-bold text-gray-900 mb-2',
                htmlContainer: 'text-gray-600 leading-relaxed',
                confirmButton: 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mr-3',
                cancelButton: 'bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-300 border border-gray-300 hover:border-gray-400'
            },
            buttonsStyling: false,
            reverseButtons: true,
            focusConfirm: false,
            allowOutsideClick: true,
            allowEscapeKey: true
        };

        function showSuccessToast() {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'This is a beautiful success message!',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true,
                background: 'linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%)',
                color: '#065f46',
                iconColor: '#10b981',
                didOpen: (toast) => {
                    toast.style.border = '1px solid #a7f3d0';
                    toast.style.borderLeft = '4px solid #10b981';
                    toast.style.borderRadius = '12px';
                    toast.style.boxShadow = '0 10px 25px rgba(16, 185, 129, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05)';
                    toast.style.fontFamily = 'Inter, system-ui, sans-serif';
                }
            });
        }

        function showErrorToast() {
            Swal.fire({
                ...defaultConfig,
                icon: 'error',
                title: 'Error',
                text: 'Something went wrong. Please try again.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
                timerProgressBar: true
            });
        }

        function showWarningToast() {
            Swal.fire({
                ...defaultConfig,
                icon: 'warning',
                title: 'Warning',
                text: 'Please review your input before proceeding.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4500,
                timerProgressBar: true
            });
        }

        function showInfoToast() {
            Swal.fire({
                ...defaultConfig,
                icon: 'info',
                title: 'Information',
                text: 'Here is some useful information for you.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
        }

        async function showDeleteConfirmation() {
            const result = await Swal.fire({
                ...defaultConfig,
                icon: 'warning',
                title: 'Delete User?',
                html: `
                    <div style="text-align: left;">
                        <p style="color: #374151; margin-bottom: 12px;">
                            Are you sure you want to delete <strong style="color: #dc2626;">John Doe</strong>?
                        </p>
                        <p style="font-size: 14px; color: #dc2626; background: #fef2f2; padding: 12px; border-radius: 8px; border: 1px solid #fecaca;">
                            This will permanently remove all user data and cannot be undone.
                        </p>
                        <p style="font-size: 14px; color: #6b7280; margin-top: 12px;">This action cannot be undone.</p>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Yes, delete user',
                cancelButtonText: 'Cancel',
                customClass: {
                    ...defaultConfig.customClass,
                    confirmButton: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mr-3'
                }
            });
            
            if (result.isConfirmed) {
                showSuccessToast();
            }
        }

        async function showBlacklistConfirmation() {
            const result = await Swal.fire({
                ...defaultConfig,
                icon: 'warning',
                title: 'Blacklist User?',
                html: `
                    <div style="text-align: left;">
                        <p style="color: #374151; margin-bottom: 12px;">
                            Are you sure you want to blacklist <strong style="color: #dc2626;">Jane Smith</strong>?
                        </p>
                        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 16px; margin-bottom: 12px;">
                            <p style="font-size: 14px; color: #b91c1c; font-weight: 500; margin-bottom: 8px;">This will:</p>
                            <ul style="font-size: 14px; color: #dc2626; margin: 0; padding-left: 16px;">
                                <li>Suspend their account immediately</li>
                                <li>Prevent them from accessing the system</li>
                                <li>Require admin intervention to restore access</li>
                            </ul>
                        </div>
                        <p style="font-size: 14px; color: #6b7280;">This action can be reversed later if needed.</p>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Yes, blacklist user',
                cancelButtonText: 'Cancel',
                customClass: {
                    ...defaultConfig.customClass,
                    confirmButton: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mr-3'
                }
            });
            
            if (result.isConfirmed) {
                showSuccessToast();
            }
        }

        async function showGenericConfirmation() {
            const result = await Swal.fire({
                ...defaultConfig,
                icon: 'question',
                title: 'Confirm Action',
                text: 'Are you sure you want to proceed with this action?',
                showCancelButton: true,
                confirmButtonText: 'Yes, proceed',
                cancelButtonText: 'Cancel'
            });
            
            if (result.isConfirmed) {
                showSuccessToast();
            }
        }

        function showLoadingDemo() {
            Swal.fire({
                title: 'Processing your request...',
                html: '<div style="display: flex; align-items: center; justify-content: center;"><div style="width: 32px; height: 32px; border: 2px solid #e5e7eb; border-top: 2px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite;"></div></div>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                customClass: {
                    popup: 'rounded-2xl shadow-2xl border-0',
                    title: 'text-lg font-semibold text-gray-700',
                    htmlContainer: 'text-gray-600'
                }
            });
            
            setTimeout(() => {
                Swal.close();
                showSuccessToast();
            }, 3000);
        }

        function showSuccessDialog() {
            Swal.fire({
                ...defaultConfig,
                icon: 'success',
                title: 'Mission Accomplished!',
                text: 'Your disaster report has been successfully submitted and is now being reviewed by our emergency response team.',
                confirmButtonText: 'Great!',
                customClass: {
                    ...defaultConfig.customClass,
                    confirmButton: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl'
                }
            });
        }

        function showErrorDialog() {
            Swal.fire({
                ...defaultConfig,
                icon: 'error',
                title: 'Connection Failed',
                text: 'Unable to connect to the emergency response system. Please check your internet connection and try again.',
                confirmButtonText: 'Retry',
                customClass: {
                    ...defaultConfig.customClass,
                    confirmButton: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl'
                }
            });
        }
    </script>
    
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
