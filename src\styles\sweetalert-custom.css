/* Enhanced SweetAlert2 styles for DisasterWatch application */
/* Beautiful, modern styling with professional appearance */

/* Import Inter font for consistency */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Global SweetAlert2 popup styling */
.swal2-popup {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  border-radius: 16px !important;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset !important;
  border: 1px solid rgba(229, 231, 235, 0.6) !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
}

/* Add subtle animation */
.swal2-popup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  pointer-events: none;
}

/* Enhanced title styling */
.swal2-title {
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 12px !important;
  font-size: 24px !important;
  line-height: 1.3 !important;
  letter-spacing: -0.025em !important;
}

/* Enhanced content styling */
.swal2-html-container {
  color: #4b5563 !important;
  line-height: 1.6 !important;
  margin: 0 !important;
  font-size: 16px !important;
  font-weight: 400 !important;
}

/* Toast-specific styling */
.swal2-toast {
  border-radius: 12px !important;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  min-height: 80px !important;
  padding: 16px 20px !important;
}

.swal2-toast .swal2-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin-bottom: 4px !important;
  line-height: 1.4 !important;
}

.swal2-toast .swal2-html-container {
  font-size: 14px !important;
  margin: 0 !important;
  line-height: 1.5 !important;
  opacity: 0.9 !important;
}

/* Toast icons */
.swal2-toast .swal2-icon {
  width: 24px !important;
  height: 24px !important;
  margin: 0 12px 0 0 !important;
  border-width: 2px !important;
  font-size: 14px !important;
}

/* Timer progress bar */
.swal2-timer-progress-bar {
  height: 3px !important;
  background: rgba(59, 130, 246, 0.8) !important;
  border-radius: 0 0 12px 12px !important;
}

/* Enhanced button styling */
.swal2-actions {
  margin-top: 24px !important;
  gap: 12px !important;
  justify-content: center !important;
}

/* Modern confirm button */
.swal2-confirm {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow:
    0 4px 12px rgba(37, 99, 235, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
  color: white !important;
  font-family: 'Inter', sans-serif !important;
  letter-spacing: 0.025em !important;
  position: relative !important;
  overflow: hidden !important;
}

.swal2-confirm::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.swal2-confirm:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #4338ca 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow:
    0 8px 20px rgba(37, 99, 235, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

.swal2-confirm:hover::before {
  left: 100%;
}

.swal2-confirm:active {
  transform: translateY(0) !important;
  box-shadow:
    0 4px 12px rgba(37, 99, 235, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Modern cancel button */
.swal2-cancel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  color: #475569 !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  margin: 0 !important;
  font-family: 'Inter', sans-serif !important;
  letter-spacing: 0.025em !important;
  position: relative !important;
  overflow: hidden !important;
}

.swal2-cancel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.swal2-cancel:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
  border-color: #94a3b8 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.swal2-cancel:hover::before {
  left: 100%;
}

.swal2-cancel:active {
  transform: translateY(0) !important;
}

/* Button styling - Deny button (for destructive actions) */
.swal2-deny {
  background: linear-gradient(to right, #dc2626, #b91c1c) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  margin: 0 0.375rem !important;
}

.swal2-deny:hover {
  background: linear-gradient(to right, #b91c1c, #991b1b) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Actions container */
.swal2-actions {
  margin-top: 1.5rem !important;
  justify-content: center !important;
  gap: 0.75rem !important;
}

/* Icon styling */
.swal2-icon {
  margin: 1rem auto 1.5rem !important;
}

.swal2-icon.swal2-success {
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.swal2-icon.swal2-error {
  border-color: #ef4444 !important;
  color: #ef4444 !important;
}

.swal2-icon.swal2-warning {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.swal2-icon.swal2-info {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.swal2-icon.swal2-question {
  border-color: #8b5cf6 !important;
  color: #8b5cf6 !important;
}

/* Toast specific styles */
.swal2-toast {
  border-radius: 0.75rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(229, 231, 235, 0.8) !important;
}

.swal2-toast .swal2-title {
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.25rem !important;
}

.swal2-toast .swal2-html-container {
  font-size: 0.875rem !important;
  margin: 0 !important;
}

/* Timer progress bar */
.swal2-timer-progress-bar {
  background: rgba(59, 130, 246, 0.8) !important;
  height: 3px !important;
}

/* Success toast specific */
.swal2-toast.swal2-success {
  border-left: 4px solid #10b981 !important;
}

.swal2-toast.swal2-success .swal2-title {
  color: #065f46 !important;
}

.swal2-toast.swal2-success .swal2-html-container {
  color: #047857 !important;
}

/* Error toast specific */
.swal2-toast.swal2-error {
  border-left: 4px solid #ef4444 !important;
}

.swal2-toast.swal2-error .swal2-title {
  color: #7f1d1d !important;
}

.swal2-toast.swal2-error .swal2-html-container {
  color: #dc2626 !important;
}

/* Warning toast specific */
.swal2-toast.swal2-warning {
  border-left: 4px solid #f59e0b !important;
}

.swal2-toast.swal2-warning .swal2-title {
  color: #78350f !important;
}

.swal2-toast.swal2-warning .swal2-html-container {
  color: #d97706 !important;
}

/* Info toast specific */
.swal2-toast.swal2-info {
  border-left: 4px solid #3b82f6 !important;
}

.swal2-toast.swal2-info .swal2-title {
  color: #1e3a8a !important;
}

.swal2-toast.swal2-info .swal2-html-container {
  color: #2563eb !important;
}

/* Loading spinner animation */
@keyframes swal2-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.swal2-loading .swal2-html-container .animate-spin {
  animation: swal2-loading-spin 1s linear infinite;
}

/* Enhanced backdrop styling */
.swal2-backdrop-show {
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(8px) saturate(1.2) !important;
  animation: backdropFadeIn 0.3s ease-out !important;
}

/* Beautiful animations */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.2);
  }
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.swal2-show {
  animation: popupSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

.swal2-toast.swal2-show {
  animation: toastSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
}

/* Icon enhancements */
.swal2-icon {
  margin: 20px auto 24px !important;
  border-width: 3px !important;
  width: 64px !important;
  height: 64px !important;
}

.swal2-icon.swal2-success {
  border-color: #10b981 !important;
  color: #10b981 !important;
}

.swal2-icon.swal2-error {
  border-color: #ef4444 !important;
  color: #ef4444 !important;
}

.swal2-icon.swal2-warning {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.swal2-icon.swal2-info {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.swal2-icon.swal2-question {
  border-color: #8b5cf6 !important;
  color: #8b5cf6 !important;
}

/* Success checkmark animation */
.swal2-icon.swal2-success .swal2-success-ring {
  border-color: #10b981 !important;
}

.swal2-icon.swal2-success .swal2-success-fix {
  background-color: #10b981 !important;
}

.swal2-icon.swal2-success [class^='swal2-success-line'] {
  background-color: #10b981 !important;
}

/* Error X animation */
.swal2-icon.swal2-error .swal2-x-mark {
  background-color: #ef4444 !important;
}

/* Responsive design improvements */
@media (max-width: 640px) {
  .swal2-popup {
    margin: 16px !important;
    max-width: calc(100% - 32px) !important;
    padding: 24px 20px !important;
  }

  .swal2-title {
    font-size: 20px !important;
  }

  .swal2-html-container {
    font-size: 14px !important;
  }

  .swal2-confirm,
  .swal2-cancel {
    padding: 10px 20px !important;
    font-size: 13px !important;
  }

  .swal2-toast {
    margin: 8px !important;
    max-width: calc(100% - 16px) !important;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .swal2-popup {
    margin: 1rem !important;
    max-width: calc(100% - 2rem) !important;
  }
  
  .swal2-title {
    font-size: 1.25rem !important;
  }
  
  .swal2-html-container {
    font-size: 0.875rem !important;
  }
  
  .swal2-confirm,
  .swal2-cancel,
  .swal2-deny {
    padding: 0.625rem 1.25rem !important;
    font-size: 0.8125rem !important;
  }
}

/* Dark mode support (if needed in the future) */
@media (prefers-color-scheme: dark) {
  .swal2-popup {
    background: #1f2937 !important;
    border-color: #374151 !important;
  }
  
  .swal2-title {
    color: #f9fafb !important;
  }
  
  .swal2-html-container {
    color: #d1d5db !important;
  }
}

/* Focus visible for accessibility */
.swal2-popup:focus-visible {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* Animation improvements */
.swal2-show {
  animation: swal2-show 0.3s ease-out !important;
}

.swal2-hide {
  animation: swal2-hide 0.15s ease-in !important;
}

@keyframes swal2-show {
  0% {
    transform: scale(0.7) translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes swal2-hide {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(0.5) translateY(-20px);
    opacity: 0;
  }
}
